{"ICAL-2025-236D-1C04-3E41-C439-471C-444E-8770-FA2E": {"data": {"prefix": "ICAL", "year": 2025, "clientId": "MBIWPN29XJPP4M", "clientName": "zoka", "timestamp": 1749099550977, "type": "LIFETIME", "machineId": "--trial", "securityLevel": "MACHINE_SPECIFIC"}, "createdAt": "2025-06-05T04:59:10.981Z", "used": false}, "ICAL-2025-2A8A-4ABA-E51A-CFCD-2893-2FD6-F196-96E4": {"data": {"prefix": "ICAL", "year": 2025, "clientId": "MBJKFO91OGC6I9", "clientName": "8FEE71D7886F2556", "timestamp": 1749139396741, "type": "LIFETIME", "machineId": "iCode DZ ", "securityLevel": "MACHINE_SPECIFIC"}, "createdAt": "2025-06-05T16:03:16.747Z", "used": false}, "ICAL-2025-585F-8802-7532-654E-560F-7AB8-CB96-62DF": {"data": {"prefix": "ICAL", "year": 2025, "clientId": "MBJXA4TZDZUIKJ", "clientName": "Test Client", "timestamp": 1749160973303, "type": "LIFETIME", "machineId": "--trial", "securityLevel": "MACHINE_SPECIFIC"}, "createdAt": "2025-06-05T22:02:53.305Z", "used": false}, "ICAL-2025-3B4C-FF64-6B99-41D3-CE2B-D834-8A36-2F36": {"data": {"prefix": "ICAL", "year": 2025, "clientId": "MBJY1XMVFU05WD", "clientName": "zoka", "timestamp": 1749162270343, "type": "LIFETIME", "machineId": "MBJKFO91OGC6I9", "securityLevel": "MACHINE_SPECIFIC"}, "createdAt": "2025-06-05T22:24:30.345Z", "used": false}, "ICAL-2025-5E2C-4B57-C402-82FB-9A83-8F02-87DD-CA27": {"data": {"prefix": "ICAL", "year": 2025, "clientId": "MBNZGLX0LT6RK1", "clientName": "zinou", "timestamp": 1749406499316, "type": "LIFETIME", "machineId": "E69D4497B04BFB30", "securityLevel": "MACHINE_SPECIFIC"}, "createdAt": "2025-06-08T18:14:59.323Z", "used": false}, "ICAL-2025-3D4E-EE53-C1CB-CA20-8B2A-1A21-DF37-9D37": {"data": {"prefix": "ICAL", "year": 2025, "clientId": "MCOORBHC4K0MV4", "clientName": "ffff", "timestamp": 1751625771744, "type": "LIFETIME", "machineId": null, "securityLevel": "UNIVERSAL"}, "createdAt": "2025-07-04T10:42:51.745Z", "used": false}}